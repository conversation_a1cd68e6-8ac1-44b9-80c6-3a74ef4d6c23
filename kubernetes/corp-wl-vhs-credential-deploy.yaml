apiVersion: apps/v1
kind: Deployment
metadata:
  name: corp-wl-vhs-credential-deploy
  namespace: corp-wl-vhs
  labels:
    app: corp-wl-vhs-credential
spec:
  replicas: 1
  selector:
    matchLabels:
      app: corp-wl-vhs-credential
  template:
    metadata:
      labels:
        app: corp-wl-vhs-credential
      annotations:
        vault.security.banzaicloud.io/vault-addr: __VAULT_ADDR__
    spec:
      serviceAccountName: corp-wl-vhs-credential
      containers:
      - name: corp-wl-vhs-credential
        image: ************.dkr.ecr.sa-east-1.amazonaws.com/corp-wl-vhs-credential:__TAG__
        imagePullPolicy: Always
        resources:
          requests:
            memory: "600Mi"
            cpu: "80m"
          limits:
            memory: "1Gi"
            cpu: "700m"
        readinessProbe:
          httpGet:
            path: /corp-wl-vhs-credential/actuator/health
            port: 8080
            httpHeaders:
            - name: X-Custom-Header
              value: ReadinessProbe
          initialDelaySeconds: 60
          periodSeconds: 20
        livenessProbe:
          httpGet:
            path: /corp-wl-vhs-credential/actuator/health
            port: 8080
            httpHeaders:
            - name: X-Custom-Header
              value: LivenessProbe
          initialDelaySeconds: 60
          periodSeconds: 20
        envFrom:
          - configMapRef:
              name: corp-wl-vhs-credential
          - secretRef:
              name: corp-wl-vhs-credential
        ports:
        - containerPort: 8080
        - containerPort: 5005

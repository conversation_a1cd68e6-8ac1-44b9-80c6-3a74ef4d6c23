apiVersion: extensions/v1beta1
kind: Ingress
metadata:
  name: corp-wl-vhs-credential-ingress
  namespace: corp-wl-vhs
  annotations:
    kubernetes.io/ingress.class: "nginx-private"
    nginx.org/ssl-backends: "corp-wl-vhs-credential-service"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
spec:
  rules:
    - host: corp-wl-vhs-credential.__SERVICE_ENV__-cvc.com.br
      http:
        paths:
          - backend:
              serviceName: corp-wl-vhs-credential-service
              servicePort: 8080

# Build layer
FROM maven:3.6.3-jdk-11-slim AS build

ARG MVN_SETTINGS_JAVA_11

COPY . /opt/app/

WORKDIR /opt/app

RUN echo -n $MVN_SETTINGS_JAVA_11 > /opt/app/settings.xml

RUN mvn --settings /opt/app/settings.xml clean package -Dmaven.test.skip=true

# Runner layer
FROM gcr.io/distroless/java:11-nonroot

COPY --from=build /opt/app/target/corp-wl-vhs-credential*.jar corp-wl-vhs-credential.jar

EXPOSE 8080
EXPOSE 5005

CMD ["corp-wl-vhs-credential.jar"]


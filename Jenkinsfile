@Library('cvc-jen<PERSON>-lib')

final def projectConfig = this.readJSON(text: """
{
    "name": "corp-wl-vhs-credential",
    "git": {
        "repositoryUrl": "******************:Desenvolvimento-MS/corp-wl-vhs/corp-wl-vhs-credential.git"
    },
    "technology": {
        "name": "JAVA",
        "dependencyManager": "MAVEN",
        "version": "11",
        "buildCommands": {
            "buildApp": "clean package -Dmaven.test.skip=true"
        }
    },
    "docker": {
        "dockerfilePath": "Dockerfile"
    },
    "kubernetes": {
        "namespace": "corp-wl-vhs"
    }
}
""")

final commons = cvcCorpPipeline.getCommons(this, projectConfig)

pipeline {
    agent any
    stages {
        stage('Deploy TI') {
            steps {
                script {
                    deploy(this, 'TI', projectConfig, commons) {
                        
                    }
                }
            }
        }
        stage('Deploy QA') {
            steps {
                script {
                    deploy(this, 'QA', projectConfig, commons) {

                    }
                }
            }
        }
        stage('Deploy PROD') {
            steps {
                script {
                    deploy(this, 'PROD', projectConfig, commons) {

                    }
                }
            }
        }
    }
}
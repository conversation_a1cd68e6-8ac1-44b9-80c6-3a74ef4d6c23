# corp-wl-vhs-credential

Boilerplate para a criação de aplicações utilizando Spring Boot, estruturado para a infraestrutura da CVC CORP.

## 🛠️ Tecnologias e Versões

- **Java 11**
- **Spring Boot 2.7.11**
- **MongoDB**
- **Lombok**
- **Elastic APM 1.6.1**
- **Logback GELF 1.1.0**
- **ModelMapper 2.3.6**

---

[![Build Status](http://corp-jenkins01.compute.br-sao-1.cvccorp.cloud:8080/job/MS/job/MS-corp-wl-vhs-credential/badge/icon)](http://corp-jenkins01.compute.br-sao-1.cvccorp.cloud:8080/job/MS/job/MS-corp-wl-vhs-credential/)
[![Quality Gate](https://sonar.app.cvc.com.br/api/project_badges/measure?project=corp-wl-vhs-credential&metric=alert_status)](https://sonar.app.cvc.com.br/dashboard?id=corp-wl-vhs-credential)
[![Coverage](https://sonar.app.cvc.com.br/api/project_badges/measure?project=corp-wl-vhs-credential&metric=coverage)](https://sonar.app.cvc.com.br/component_measures?id=corp-wl-vhs-credential&metric=Coverage)
[![Maintainnability](https://sonar.app.cvc.com.br/api/project_badges/measure?project=corp-wl-vhs-credential&metric=sqale_rating)](https://sonar.app.cvc.com.br/component_measures?id=corp-wl-vhs-credential&metric=Maintainability)
[![Security](https://sonar.app.cvc.com.br/api/project_badges/measure?project=corp-wl-vhs-credential&metric=security_rating)](https://sonar.app.cvc.com.br/component_measures?id=corp-wl-vhs-credential&metric=Security)

---

## Requirements

### SpringBoot

- JDK 11
- Maven
- Docker (opcional para execução em container).

---

## 🚀 Como rodar o projeto localmente

### Pré-requisitos

- **JDK 11**
- **Maven**
- **Docker** (opcional para execução em container)

### Passos para execução

1. Clone o repositório:

   ```bash
   git clone http://git.cvc.com.br/Desenvolvimento-MS/corp-wl-vhs-credential.git
   cd corp-wl-vhs-credential
   ```

2. Configure o arquivo `settings.xml` dentro da pasta `.m2` da sua máquina:

   ```xml
   <?xml version="1.0" encoding="UTF-8"?>
   <settings xmlns="http://maven.apache.org/SETTINGS/1.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/SETTINGS/1.0.0 http://maven.apache.org/xsd/settings-1.0.0.xsd">
     <servers>
       <server>
         <id>central</id>
         <username>admin</username>
         <password>cvc2015@@</password>
       </server>
       <server>
         <id>snapshots</id>
         <username>admin</username>
         <password>cvc2015@@</password>
       </server>
     </servers>
     <profiles>
       <profile>
         <properties>
           <nexus.baseurl>http://nexus.cvc.com.br:8081/nexus</nexus.baseurl>
         </properties>
         <repositories>
           <repository>
             <snapshots>
               <enabled>false</enabled>
             </snapshots>
             <id>central</id>
             <name>libs-release</name>
             <url>http://nexus.cvc.com.br:8081/nexus/content/groups/public</url>
           </repository>
           <repository>
             <snapshots>
               <enabled>true</enabled>
             </snapshots>
             <id>snapshots</id>
             <name>libs-snapshot</name>
             <url>http://nexus.cvc.com.br:8081/nexus/content/groups/public</url>
           </repository>
         </repositories>
       </profile>
     </profiles>
     <activeProfiles>
       <activeProfile>nexus</activeProfile>
     </activeProfiles>
   </settings>
   ```

3. Compile e empacote o projeto:

   ```bash
   mvn clean install
   ```

4. Configure as variáveis de ambiente necessárias:

   - Consulte os `configmaps` no Kubernetes para identificar os valores necessários.
   - Configure a variável `VAULT_SECURITY_TOKEN` com o valor correspondente.

5. Execute o projeto:

   ```bash
   mvn spring-boot:run
   ```

6. Acesse a aplicação:
   [http://localhost:8080](http://localhost:8080)

---

## 🌐 URLs dos Ambientes

### Produção (PRD)

- **Base URL:** [https://corp-wl-vhs-credential.k8s-cvc.com.br](https://corp-wl-vhs-credential.k8s-cvc.com.br)
- **Swagger:** [https://corp-wl-vhs-credential.k8s-cvc.com.br/swagger-ui.html](https://corp-wl-vhs-credential.k8s-cvc.com.br/swagger-ui.html)

### Homologação (QA)

- **Base URL:** [https://corp-wl-vhs-credential.k8s-qa-cvc.com.br](https://corp-wl-vhs-credential.k8s-qa-cvc.com.br)
- **Swagger:** [https://corp-wl-vhs-credential.k8s-qa-cvc.com.br/swagger-ui.html](https://corp-wl-vhs-credential.k8s-qa-cvc.com.br/swagger-ui.html)

### Desenvolvimento (TI)

- **Base URL:** [https://corp-wl-vhs-credential.k8s-ti-cvc.com.br](https://corp-wl-vhs-credential.k8s-ti-cvc.com.br)
- **Swagger:** [https://corp-wl-vhs-credential.k8s-ti-cvc.com.br/swagger-ui.html](https://corp-wl-vhs-credential.k8s-ti-cvc.com.br/swagger-ui.html)

### Local

- **Base URL:** [http://localhost:8080](http://localhost:8080)
- **Swagger:** [http://localhost:8080/corp-wl-vhs-credential/swagger-ui/index.html#/](http://localhost:8080/corp-wl-vhs-credential/swagger-ui/index.html#/)

---

## 📂 Estrutura do Projeto

- **Framework:** Spring Boot
- **Dependências Principais:**
  - `spring-boot-starter-data-mongodb`
  - `spring-boot-starter-web`
  - `spring-boot-actuator`
  - `spring-cloud-starter-consul-config`
  - `springfox-swagger2`
  - `springfox-swagger-ui`
  - `modelmapper`
  - `lombok`

---

## 🔍 Principais Scripts Disponíveis

1. **Limpar e compilar o projeto:**

   ```bash
   mvn clean install
   ```

2. **Rodar o projeto localmente:**

   ```bash
   mvn spring-boot:run
   ```

3. **Executar testes:**

   ```bash
   mvn test
   ```

4. **Gerar imagem Docker:**
   ```bash
   docker build -t corp-wl-vhs-credential .
   ```

---

## Jenkins

Acesse o pipeline no Jenkins para integração e deploy contínuo:

[Pipeline Jenkins](http://corp-jenkins01.compute.br-sao-1.cvccorp.cloud:8080/job/MS/job/MS-corp-wl-vhs-credential/)

---

## Repositório Git

[Repositório Git](http://git.cvc.com.br/Desenvolvimento-MS/corp-wl-vhs-credential.git)

---

## Arquitetura

Para mais informações sobre a arquitetura, consulte a documentação específica do projeto.

## 📊 MongoDB

### Configuração

O projeto utiliza MongoDB como banco de dados principal. A configuração é feita através das seguintes variáveis de ambiente:

- `SPRING_DATASOURCE_MONGO_USERNAME`: Usuário do MongoDB
- `SPRING_DATASOURCE_MONGO_PASSWORD`: Senha do MongoDB
- `spring.datasource.mongo.host`: Host do MongoDB

A string de conexão é construída automaticamente com os seguintes parâmetros:

- Porta: 27017
- Autenticação: PLAIN
- SSL: true
- Retry Writes: false
- Load Balanced: true

### Exemplo de Uso

O projeto possui endpoints para gerenciar credenciais no MongoDB. Aqui está um exemplo de como usar:

#### Buscar Credencial

```http
curl --location 'http://localhost:8080/corp-wl-vhs-credential/credential/LOJ/1?transactionId=c32a92cc-d21d-4b9d-9dca-74b1cb5ad1c9' \
--header 'Gtw-Transaction-Id: c32a92cc-d21d-4b9d-9dca-74b1cb5ad1c9'
```

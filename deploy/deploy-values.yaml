environment:
  - name: TI
    farm:
      - name: default
        kubernetes:
          deployment:
            resources:
              requests:
                memory: 600Mi
                cpu: 80m
              limits:
                memory: 1Gi
                cpu: 700m
            readinessProbe:
              health-check:
                path: /corp-wl-vhs-credential/actuator/health
                port: 8080
              initialDelaySeconds: 60
            livenessProbe:
              health-check:
                path: /corp-wl-vhs-credential/actuator/health
                port: 8080
              initialDelaySeconds: 60
            ports:
              - containerPort: 8080
              - containerPort: 5005
          hpa:
            minReplicas: 1
            maxReplicas: 10
            metrics:
              resources:
                - name: cpu
                  target:
                    type: Utilization
                    averageUtilization: 80
                - name: memory
                  target:
                    type: Utilization
                    averageUtilization: 250
          service:
            ports:
              - name: mainport
                port: 8080
              - name: secport
                port: 5005
          ingress:
            externalDnsType: nginx-private
            ingressClassName: nginx-private
            type: http
            rules:
              - host: corp-wl-vhs-credential
                service:
                  port:
                    number: 8080
          configmap:
            data:
              - name: PREFIX
                value: corp-wl-vhs
              - name: APPLICATION_NAME
                value: corp-wl-vhs-credential
              - name: CONSUL_HOST
                value: consul-dev.services.cvc.com.br
              - name: CONSUL_PORT
                value: "8500"
              - name: ELASTIC_APM_SERVICE_NAME
                value: corp-wl-vhs-credential
              - name: ELASTIC_APM_APPLICATION_PACKAGES
                value: br.com.cvc.corpwlvhscredential
              - name: ELASTIC_APM_SERVER_URLS
                value: https://apm-server.services.cvc.com.br:8200
              - name: ADDITIONAL_OPTS
                value: -javaagent:./apm/elastic-apm-agent-1.1.1.jar  -Dspring.profiles.active=ti
  - name: QA-TMP
    farm:
      - name: default
        kubernetes:
          deployment:
            resources:
              requests:
                memory: 600Mi
                cpu: 80m
              limits:
                memory: 1Gi
                cpu: 700m
            readinessProbe:
              health-check:
                path: /corp-wl-vhs-credential/actuator/health
                port: 8080
              initialDelaySeconds: 60
            livenessProbe:
              health-check:
                path: /corp-wl-vhs-credential/actuator/health
                port: 8080
              initialDelaySeconds: 60
            ports:
              - containerPort: 8080
              - containerPort: 5005
          hpa:
            minReplicas: 1
            maxReplicas: 10
            metrics:
              resources:
                - name: cpu
                  target:
                    type: Utilization
                    averageUtilization: 80
                - name: memory
                  target:
                    type: Utilization
                    averageUtilization: 250
          service:
            ports:
              - name: mainport
                port: 8080
              - name: secport
                port: 5005
          ingress:
            externalDnsType: nginx-private
            ingressClassName: nginx-private
            type: http
            rules:
              - host: corp-wl-vhs-credential
                service:
                  port:
                    number: 8080
          configmap:
            data:
              - name: APPLICATION_NAME
                value: corp-wl-vhs-credential
              - name: CONSUL_HOST
                value: consul.qa.cvc.intra
              - name: CONSUL_PORT
                value: "8500"
              - name: ELASTIC_APM_SERVICE_NAME
                value: corp-wl-vhs-credential
              - name: ELASTIC_APM_APPLICATION_PACKAGES
                value: br.com.cvc.corpwlvhscredential
              - name: ELASTIC_APM_SERVER_URLS
                value: https://apm-server.services.cvc.com.br:8200
              - name: ADDITIONAL_OPTS
                value: -javaagent:./apm/elastic-apm-agent-1.1.1.jar  -Dspring.profiles.active=qa
              - name: PREFIX
                value: corp-wl-vhs
  - name: QA
    farm:
      - name: default
        kubernetes:
          deployment:
            resources:
              requests:
                memory: 600Mi
                cpu: 80m
              limits:
                memory: 1Gi
                cpu: 700m
            readinessProbe:
              health-check:
                path: /corp-wl-vhs-credential/actuator/health
                port: 8080
              initialDelaySeconds: 60
            livenessProbe:
              health-check:
                path: /corp-wl-vhs-credential/actuator/health
                port: 8080
              initialDelaySeconds: 60
            ports:
              - containerPort: 8080
              - containerPort: 5005
          hpa:
            minReplicas: 1
            maxReplicas: 10
            metrics:
              resources:
                - name: cpu
                  target:
                    type: Utilization
                    averageUtilization: 80
                - name: memory
                  target:
                    type: Utilization
                    averageUtilization: 250
          service:
            ports:
              - name: mainport
                port: 8080
              - name: secport
                port: 5005
          ingress:
            externalDnsType: nginx-private
            ingressClassName: nginx-private
            type: http
            rules:
              - host: corp-wl-vhs-credential
                service:
                  port:
                    number: 8080
          configmap:
            data:
              - name: APPLICATION_NAME
                value: corp-wl-vhs-credential
              - name: CONSUL_HOST
                value: consul.qa.cvc.intra
              - name: CONSUL_PORT
                value: "8500"
              - name: ELASTIC_APM_SERVICE_NAME
                value: corp-wl-vhs-credential
              - name: ELASTIC_APM_APPLICATION_PACKAGES
                value: br.com.cvc.corpwlvhscredential
              - name: ELASTIC_APM_SERVER_URLS
                value: https://apm-server.services.cvc.com.br:8200
              - name: ADDITIONAL_OPTS
                value: -javaagent:./apm/elastic-apm-agent-1.1.1.jar  -Dspring.profiles.active=qa
              - name: PREFIX
                value: corp-wl-vhs
  - name: PILOT
    farm:
      - name: default
        kubernetes:
          deployment:
            resources:
              requests:
                memory: 600Mi
                cpu: 80m
              limits:
                memory: 1Gi
                cpu: 700m
            readinessProbe:
              health-check:
                path: /corp-wl-vhs-credential/actuator/health
                port: 8080
              initialDelaySeconds: 60
            livenessProbe:
              health-check:
                path: /corp-wl-vhs-credential/actuator/health
                port: 8080
              initialDelaySeconds: 60
            ports:
              - containerPort: 8080
              - containerPort: 5005
          hpa:
            minReplicas: 1
            maxReplicas: 10
            metrics:
              resources:
                - name: cpu
                  target:
                    type: Utilization
                    averageUtilization: 80
                - name: memory
                  target:
                    type: Utilization
                    averageUtilization: 250
          service:
            ports:
              - name: mainport
                port: 8080
              - name: secport
                port: 5005
          ingress:
            externalDnsType: nginx-private
            ingressClassName: nginx-private
            type: http
            rules:
              - host: corp-wl-vhs-credential
                service:
                  port:
                    number: 8080
          configmap:
            data:
              - name: CONSUL_HOST
                value: consul.prod.cvc.intra
              - name: CONSUL_PORT
                value: "8500"
              - name: ELASTIC_APM_SERVICE_NAME
                value: corp-wl-vhs-credential
              - name: ELASTIC_APM_APPLICATION_PACKAGES
                value: br.com.cvc.corpwlvhscredential
              - name: ELASTIC_APM_SERVER_URLS
                value: https://apm-server.services.cvc.com.br:8200
              - name: ADDITIONAL_OPTS
                value: -javaagent:./apm/elastic-apm-agent-1.1.1.jar  -Dspring.profiles.active=prod
              - name: PREFIX
                value: corp-wl-vhs
              - name: APPLICATION_NAME
                value: corp-wl-vhs-credential
  - name: PROD
    farm:
      - name: default
        kubernetes:
          deployment:
            resources:
              requests:
                memory: 600Mi
                cpu: 80m
              limits:
                memory: 1Gi
                cpu: 700m
            readinessProbe:
              health-check:
                path: /corp-wl-vhs-credential/actuator/health
                port: 8080
              initialDelaySeconds: 60
            livenessProbe:
              health-check:
                path: /corp-wl-vhs-credential/actuator/health
                port: 8080
              initialDelaySeconds: 60
            ports:
              - containerPort: 8080
              - containerPort: 5005
          hpa:
            minReplicas: 1
            maxReplicas: 10
            metrics:
              resources:
                - name: cpu
                  target:
                    type: Utilization
                    averageUtilization: 80
                - name: memory
                  target:
                    type: Utilization
                    averageUtilization: 250
          service:
            ports:
              - name: mainport
                port: 8080
              - name: secport
                port: 5005
          ingress:
            externalDnsType: nginx-private
            ingressClassName: nginx-private
            type: http
            rules:
              - host: corp-wl-vhs-credential
                service:
                  port:
                    number: 8080
          configmap:
            data:
              - name: CONSUL_HOST
                value: consul.prod.cvc.intra
              - name: CONSUL_PORT
                value: "8500"
              - name: ELASTIC_APM_SERVICE_NAME
                value: corp-wl-vhs-credential
              - name: ELASTIC_APM_APPLICATION_PACKAGES
                value: br.com.cvc.corpwlvhscredential
              - name: ELASTIC_APM_SERVER_URLS
                value: https://apm-server.services.cvc.com.br:8200
              - name: ADDITIONAL_OPTS
                value: -javaagent:./apm/elastic-apm-agent-1.1.1.jar  -Dspring.profiles.active=prod
              - name: PREFIX
                value: corp-wl-vhs
              - name: APPLICATION_NAME
                value: corp-wl-vhs-credential

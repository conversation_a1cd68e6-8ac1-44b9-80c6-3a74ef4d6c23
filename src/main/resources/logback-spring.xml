<?xml version="1.0" encoding="UTF-8"?>
<configuration>

    <include resource="org/springframework/boot/logging/logback/defaults.xml"/>

    <!-- GLOBAL PROPERTIES -->
    <springProperty name="APP_NAME" scope="context" source="spring.application.name" defaultValue="corp-wl-vhs-credential"/>

    <!-- https://stackoverflow.com/questions/75100707/how-to-resolve-warning-in-logback-debug-mode-when-using-a-custom-file-appender-l -->
    <statusListener class="ch.qos.logback.core.status.NopStatusListener"/>

    <!-- PROPERTIES PER ENVIRONMENT -->
    <springProfile name="ti">
        <property name="LOGSTASH_HOST" value="logstash-ti.services.cvc.com.br"/>
        <property name="LOGSTASH_PORT" value="12201"/>
    </springProfile>

    <springProfile name="qa">
        <property name="LOGSTASH_HOST" value="logstash-qa.services.cvc.com.br"/>
        <property name="LOGSTASH_PORT" value="12201"/>
    </springProfile>

    <springProfile name="prod">
        <property name="LOGSTASH_HOST" value="logstash.services.cvc.com.br"/>
        <property name="LOGSTASH_PORT" value="12201"/>
    </springProfile>

    <!-- LOCAL PROFILE -->
    <springProfile name="default,local,local-ti,local-qa,local-prod,test">
        <property name="STDOUT_LOG_PATTERN"
                  value="%d{yyyy-MM-dd} | %d{HH:mm:ss.SSS} | %boldCyan(%-15.15thread) | %highlight(%-5level) | %boldGreen(%-30.30logger{30}) | %m%n"/>

    </springProfile>

    <!-- K8S PROFILE-->
    <springProfile name="ti, qa, prod">
        <property name="STDOUT_LOG_PATTERN"
                  value="%d{yyyy-MM-dd} | %d{HH:mm:ss.SSS} | %-15.15thread | %-5level | %-30.30logger{30} | %m%n"/>

    </springProfile>

    <!-- APPENDER LOGSTASH  -->
    <springProfile name="ti, qa, prod">
        <appender name="LOGSTASH" class="de.siegmar.logbackgelf.GelfUdpAppender">
            <graylogHost>${LOGSTASH_HOST}</graylogHost>
            <graylogPort>${LOGSTASH_PORT}</graylogPort>
            <maxChunkSize>508</maxChunkSize>
            <useCompression>true</useCompression>
            <encoder class="de.siegmar.logbackgelf.GelfEncoder">
                <charset>UTF-8</charset>
                <includeRawMessage>false</includeRawMessage>
                <includeMarker>true</includeMarker>
                <includeMdcData>true</includeMdcData>
                <includeCallerData>true</includeCallerData>
                <includeRootCauseData>false</includeRootCauseData>
                <includeLevelName>true</includeLevelName>
                <shortPatternLayout class="ch.qos.logback.classic.PatternLayout">
                    <pattern>%m%nopex</pattern>
                </shortPatternLayout>
                <fullPatternLayout class="ch.qos.logback.classic.PatternLayout">
                    <pattern>%m</pattern>
                </fullPatternLayout>
                <staticField>app_name:${APP_NAME}</staticField>
            </encoder>
        </appender>

        <root>
            <appender-ref ref="LOGSTASH"/>
        </root>

    </springProfile>

    <!-- APPENDER STDOUT  -->
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${STDOUT_LOG_PATTERN}</pattern>
            <charset>utf8</charset>
        </encoder>
    </appender>

    <root>
        <appender-ref ref="STDOUT"/>
    </root>


</configuration>
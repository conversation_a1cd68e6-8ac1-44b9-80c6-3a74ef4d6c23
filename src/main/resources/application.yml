secret: ${SPRING_DECRYPTY}

spring:
  application.name: ${APPLICATION_NAME}
  config:
    import:
      - optional:application-local.yml
      - optional:application-local-qa.yml
      - optional:application-local-prod.yml
      - optional:application-test.yml
      - optional:consul://
  data:
    mongodb:
      uri: "mongodb://${SPRING_DATASOURCE_MONGO_USERNAME}:${SPRING_DATASOURCE_MONGO_PASSWORD}@${spring.datasource.mongo.host}/${spring.datasource.mongo.database}?authMechanism=PLAIN&authSource=$external&ssl=true&retryWrites=false&loadBalanced=true"
  output:
    ansi:
      enabled: always
  cloud:
    consul:
      host: ${CONSUL_HOST}
      port: ${CONSUL_PORT}
      config:
        enabled: true
        prefixes: ${PREFIX}
        default-context: ${APPLICATION_NAME}
        format: key_value
        fail-fast: false
  jackson:
    default-property-inclusion: non_null

logging:
  level:
    root: ERROR
    org.springframework.data.mongodb: DEBUG
    br.com.cvc.corpwlvhscredential: DEBUG

server:
  servlet:
    context-path: /corp-wl-vhs-credential

management:
  endpoint:
    health:
      show-details: always
    env:
      enabled: true
  endpoints:
    web:
      exposure:
        include: env, health, info, loggers

feign:
  hystrix:
    enabled: true
  compression:
    request:
      enabled: true
    response:
      enabled: true
hystrix:
  command:
    default:
      execution:
        isolation:
          thread:
            timeoutInMilliseconds: 30000
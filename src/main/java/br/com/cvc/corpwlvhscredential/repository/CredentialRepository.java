package br.com.cvc.corpwlvhscredential.repository;



import br.com.cvc.corpwlvhscredential.model.Credential;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface CredentialRepository extends MongoRepository<Credential, String> {

	Optional<Credential> findByBusinessUnitAndCompanyId(String businessUnit, Long companyId);

	void deleteByBusinessUnitAndCompanyId(String businessUnit, Long companyId);

}

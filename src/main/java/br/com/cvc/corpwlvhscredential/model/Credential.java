package br.com.cvc.corpwlvhscredential.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

@Document
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Credential {
	@Id
	private String id;

	private String businessUnit;

	private Long companyId;

	private String token;

}

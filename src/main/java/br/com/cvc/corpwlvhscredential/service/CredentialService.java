package br.com.cvc.corpwlvhscredential.service;


import br.com.cvc.corpwlvhscredential.dto.CredentialRequestDTO;
import br.com.cvc.corpwlvhscredential.dto.CredentialUpdateDTO;
import br.com.cvc.corpwlvhscredential.model.Credential;
import br.com.cvc.corpwlvhscredential.repository.CredentialRepository;
import co.elastic.apm.api.CaptureSpan;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Optional;

@Slf4j
@Service
@RequiredArgsConstructor
public class CredentialService {

	private final CredentialRepository repository;
	private final CryptoService cripto;

	@CaptureSpan
	public Optional<Credential> save(final CredentialRequestDTO request) {

		Credential credential = Credential.builder()
				.businessUnit(request.getBusinessUnit())
				.companyId(request.getCompanyId())
				.token(cripto.encrypt(request.getUsername(), request.getPassword()))
				.build();

		return Optional.of(repository.save(credential));
	}

	@CaptureSpan
	public Optional<Credential> update(final String businessUnit, final Long companyId, final CredentialUpdateDTO request) {

		Optional<Credential> credencial = repository.findByBusinessUnitAndCompanyId(businessUnit, companyId);

		if (credencial.isPresent()) {
			Credential update = credencial.get();
			update.setToken(cripto.encrypt(request.getUsername(), request.getPassword()));
			return Optional.of(repository.save(update));
		} else
			throw new RuntimeException("Nao e possivel atualizar");
	}

	@CaptureSpan
	public Collection<Credential> findAll() {
		return repository.findAll();
	}

	@CaptureSpan
	public Optional<Credential> findByBusinessUnitAndCompanyId(final String businessUnit, final Long companyId) {
		return repository.findByBusinessUnitAndCompanyId(businessUnit, companyId);
	}

	@CaptureSpan
	public void deleteByBusinessIdAndCompanyId(final String businessUnit, final Long companyId) {
		repository.deleteByBusinessUnitAndCompanyId(businessUnit, companyId);
	}

	public void deleteAll() {
		repository.deleteAll();
	}
}

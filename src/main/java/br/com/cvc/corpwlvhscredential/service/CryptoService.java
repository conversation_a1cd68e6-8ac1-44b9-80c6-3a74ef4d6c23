package br.com.cvc.corpwlvhscredential.service;

import co.elastic.apm.api.CaptureSpan;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Hex;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import javax.validation.constraints.NotNull;

@Slf4j
@Service
public class CryptoService {

	@NotNull
	@Value("${secret}")
	private String secret;

	@CaptureSpan
	public String encrypt(final String username, final String password) {
		try {
			byte[] token = (username + ":" + password).getBytes("UTF-8");
			byte[] key = secret.getBytes("UTF-8");

			Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
			cipher.init(Cipher.ENCRYPT_MODE, new SecretKeySpec(key, "AES"));
			byte[] encrypted = cipher.doFinal(token);

			return Hex.encodeHexString(encrypted);
		} catch (Exception e) {
			log.error("ERROR: " + e);
			throw new RuntimeException(e);
		}
	}
}

package br.com.cvc.corpwlvhscredential.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CredentialRequestDTO {

	@NotBlank(message = "Campo businessUnit não pode ser vazio")
	@NotNull(message = "Campo businessUnit é obrigatório")
	private String businessUnit;

	@NotNull(message = "Campo companyId é obrigatório")
	private Long companyId;

	@NotBlank(message = "Campo username não pode ser vazio")
	@NotNull(message = "Campo username é obrigatório")
	private String username;

	@NotBlank(message = "Campo password não pode ser vazio")
	@NotNull(message = "Campo password é obrigatório")
	private String password;


}

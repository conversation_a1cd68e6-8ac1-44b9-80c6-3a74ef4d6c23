package br.com.cvc.corpwlvhscredential.transformation;



import br.com.cvc.corpwlvhscredential.dto.CredentialResponseDTO;
import br.com.cvc.corpwlvhscredential.model.Credential;
import org.springframework.stereotype.Service;

@Service
public class CredentialTransformation {
	public CredentialResponseDTO convert(Credential credential) {
		return CredentialResponseDTO.builder()
				.businessUnit(credential.getBusinessUnit())
				.companyId(credential.getCompanyId())
				.token(credential.getToken())
				.id(credential.getId())
				.build();
	}

}

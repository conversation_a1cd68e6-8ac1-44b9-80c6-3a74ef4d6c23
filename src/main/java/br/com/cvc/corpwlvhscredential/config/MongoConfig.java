package br.com.cvc.corpwlvhscredential.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

@Component
@Slf4j
public class MongoConfig {

    private final Environment environment;

    public MongoConfig(Environment environment) {
        this.environment = environment;
    }

    @PostConstruct
    public void logMongoUri() {
        String mongoUri = environment.getProperty("spring.data.mongodb.uri");
        log.debug("Mongo URI: {}", mongoUri);
    }
}
package br.com.cvc.corpwlvhscredential.config;

import org.jboss.logging.MDC;
import org.springframework.stereotype.Component;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;

@Component
public class MDCConfig implements Filter {

    @Override
    public void destroy() {
    }

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
    }

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain) throws IOException, ServletException {
        String correlationId = ((HttpServletRequest) servletRequest).getHeader("Gtw-Transaction-Id");
        String userName = ((HttpServletRequest) servletRequest).getHeader("Gtw-Username");
        String agentSign = ((HttpServletRequest) servletRequest).getHeader("Gtw-Agent-Sign");
        String agencyId = ((HttpServletRequest) servletRequest).getHeader("Gtw-Agency-Id");
        String branchId = ((HttpServletRequest) servletRequest).getHeader("Gtw-Branch-Id");
        MDC.put("transactionId", correlationId);
        MDC.put("username", userName);
        MDC.put("agentSign", agentSign);
        MDC.put("agencyId", agencyId);
        MDC.put("branchId", branchId);
        
        filterChain.doFilter(servletRequest, servletResponse);
    }

}

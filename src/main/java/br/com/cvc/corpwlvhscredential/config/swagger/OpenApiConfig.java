package br.com.cvc.corpwlvhscredential.config.swagger;


import io.swagger.v3.oas.annotations.OpenAPIDefinition;
import io.swagger.v3.oas.annotations.info.Contact;
import io.swagger.v3.oas.annotations.info.Info;
import org.springframework.context.annotation.Configuration;

@Configuration
@OpenAPIDefinition(
        info = @Info(
                title = "corp-wl-vhs-credential",
                version = "1.0.0",
                description = "",
                license = @io.swagger.v3.oas.annotations.info.License(name = "MIT"),
                contact = @Contact(name = "Squad Pacote", email = "<EMAIL>")
        )
)
public class OpenApiConfig {

}

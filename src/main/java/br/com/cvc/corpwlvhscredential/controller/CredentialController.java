package br.com.cvc.corpwlvhscredential.controller;



import br.com.cvc.corpwlvhscredential.dto.CredentialRequestDTO;
import br.com.cvc.corpwlvhscredential.dto.CredentialResponseDTO;
import br.com.cvc.corpwlvhscredential.dto.CredentialUpdateDTO;
import br.com.cvc.corpwlvhscredential.exception.CredentialException;
import br.com.cvc.corpwlvhscredential.exception.NotFoundException;
import br.com.cvc.corpwlvhscredential.service.CredentialService;
import br.com.cvc.corpwlvhscredential.transformation.CredentialTransformation;
import co.elastic.apm.api.CaptureTransaction;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Collection;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@RestController
@Tag(name = "Credential")
@RequiredArgsConstructor
public class CredentialController {

	private final CredentialService service;
	private final CredentialTransformation transformation;


	@PostMapping(value = "/credential", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	@ResponseStatus(HttpStatus.CREATED)
	@CaptureTransaction
	public CredentialResponseDTO save(
			@Valid @RequestBody CredentialRequestDTO request,
			@RequestHeader("Gtw-Transaction-Id") String transactionId) {
		try {
			log.info("START Save Credential");

			final CredentialResponseDTO response = this.service.save(request)
					.filter(Objects::nonNull)
					.map(transformation::convert)
					.orElseThrow(() -> {
						log.error("ERROR: Não foi possível gravar as credenciais para a Unidade de Negócio {}", request.getBusinessUnit());
						return new RuntimeException("Não foi possível gravar as credenciais para a Unidade de Negócio " + request.getBusinessUnit());
					});

			log.info("END Save Credential");

			return response;

		} catch (Exception e) {
			log.error("ERROR Save Credential: " + e);
			throw new CredentialException(e.getMessage());
		}
	}

	@PutMapping(value = "/credential/{businessUnit}/{companyId}", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	@CaptureTransaction
	public CredentialResponseDTO update(
			@Valid @RequestBody CredentialUpdateDTO request,
			@PathVariable(value = "businessUnit") String businessUnit,
			@PathVariable(value = "companyId") Long companyId,
			@RequestHeader("Gtw-Transaction-Id") String transactionId) {
		try {
			log.info("START Save Credential");

			final CredentialResponseDTO response = this.service.update(businessUnit, companyId, request)
					.filter(Objects::nonNull)
					.map(transformation::convert)
					.orElseThrow(() -> {
						log.error("ERROR: Não foi possível alterar a credencial para a Unidade de Negócio {}", businessUnit);
						return new RuntimeException("Não foi alterar gravar a credencial para a Unidade de Negócio " + businessUnit);
					});

			log.info("END Save Credential");

			return response;

		} catch (Exception e) {
			log.error("ERROR Save Credential: " + e);
			throw new CredentialException(e.getMessage());
		}
	}

	@GetMapping(value = "/credential", produces = MediaType.APPLICATION_JSON_VALUE)
	@CaptureTransaction
	Collection<CredentialResponseDTO> findAll(
			@RequestHeader(value = "Gtw-Transaction-Id") String transactionId) {
		try {
			log.info("START FindAll Credential");
			

			Collection<CredentialResponseDTO> response = this.service.findAll()
					.stream()
					.filter(Objects::nonNull)
					.map(transformation::convert)
					.collect(Collectors.toList());

			log.info("END FindAll Credential");

			return response;

		} catch (NotFoundException e) {
			log.error("ERROR FindAll Credential: " + e);
			throw e;
		} catch (Exception e) {
			log.error("ERROR FindAll Credential: " + e);
			throw new CredentialException(e.getMessage());
		}
	}

	@GetMapping(value = "/credential/{businessUnit}/{companyId}", produces = MediaType.APPLICATION_JSON_VALUE)
	@CaptureTransaction
	CredentialResponseDTO findById(
			@PathVariable(value = "businessUnit") String businessUnit,
			@PathVariable(value = "companyId") Long companyId,
			@RequestHeader(value = "Gtw-Transaction-Id") String transactionId) {
		try {
			log.info("START FindById Credential");

			CredentialResponseDTO response = this.service.findByBusinessUnitAndCompanyId(businessUnit, companyId)
					.filter(Objects::nonNull)
					.filter(x -> Objects.equals(x.getCompanyId(), companyId))
					.map(transformation::convert)
					.orElseThrow(() -> {
						log.error("ERROR: Não foi encontrado a credencial para a Unidade de Negócio {}", businessUnit);
						return new NotFoundException("Não foi encontrado a credencial para a Unidade de Negócio " + businessUnit);
					});

			log.info("END FindById Credential");

			return response;

		} catch (NotFoundException e) {
			log.error("ERROR FindById Credential: " + e);
			throw e;
		} catch (Exception e) {
			log.error("ERROR FindById Credential: " + e);
			throw new CredentialException(e.getMessage());
		}
	}

	@DeleteMapping(value = "/credential/{businessUnit}/{companyId}", produces = MediaType.APPLICATION_JSON_VALUE)
	@CaptureTransaction
	void delete(
			@PathVariable(value = "companyId") Long companyId,
			@PathVariable(value = "businessUnit") String businessUnit,
			@RequestHeader(value = "Gtw-Transaction-Id") String transactionId) {
		try {
			log.info("START Delete Credential");
			this.service.deleteByBusinessIdAndCompanyId(businessUnit, companyId);
			log.info("END Delete Credential");
		} catch (Exception e) {
			log.error("ERROR Delete Credential: " + e);
			throw new CredentialException(e.getMessage());
		}
	}

	@DeleteMapping("/deleteAll")
	public void deleteAll() {
		this.service.deleteAll();
	}
}
